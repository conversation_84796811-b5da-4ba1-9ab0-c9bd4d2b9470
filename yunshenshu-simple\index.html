<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>云深处 - 探索云端科技</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        /* 导航栏 */
        .navbar {
            position: fixed;
            top: 0;
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            z-index: 1000;
            padding: 15px 0;
        }
        
        .nav-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: white;
        }
        
        .nav-links {
            display: flex;
            list-style: none;
            gap: 30px;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            transition: opacity 0.3s;
        }
        
        .nav-links a:hover {
            opacity: 0.8;
        }
        
        /* 主要内容 */
        .hero {
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: white;
            position: relative;
        }
        
        .hero-content h1 {
            font-size: 4rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .hero-content p {
            font-size: 1.2rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        
        .btn {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid white;
            color: white;
            padding: 12px 30px;
            border-radius: 50px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s;
            margin: 0 10px;
        }
        
        .btn:hover {
            background: white;
            color: #667eea;
        }
        
        /* 云朵动画 */
        .clouds {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        
        .cloud {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50px;
            opacity: 0.6;
            animation: float 20s infinite linear;
        }
        
        .cloud:before {
            content: '';
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50px;
        }
        
        .cloud1 {
            width: 100px;
            height: 40px;
            top: 20%;
            animation-duration: 25s;
        }
        
        .cloud1:before {
            width: 50px;
            height: 50px;
            top: -25px;
            left: 10px;
        }
        
        .cloud2 {
            width: 80px;
            height: 30px;
            top: 60%;
            animation-duration: 30s;
            animation-delay: -10s;
        }
        
        .cloud2:before {
            width: 40px;
            height: 40px;
            top: -20px;
            left: 15px;
        }
        
        @keyframes float {
            0% {
                left: -150px;
            }
            100% {
                left: 100%;
            }
        }
        
        /* 服务区域 */
        .services {
            padding: 80px 0;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .services h2 {
            text-align: center;
            color: white;
            font-size: 2.5rem;
            margin-bottom: 50px;
        }
        
        .service-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
        }
        
        .service-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            color: white;
            transition: transform 0.3s;
        }
        
        .service-card:hover {
            transform: translateY(-10px);
        }
        
        .service-icon {
            font-size: 3rem;
            margin-bottom: 20px;
        }
        
        .service-card h3 {
            margin-bottom: 15px;
        }
        
        /* 联系区域 */
        .contact {
            padding: 80px 0;
            text-align: center;
            color: white;
        }
        
        .contact h2 {
            font-size: 2.5rem;
            margin-bottom: 30px;
        }
        
        .contact-form {
            max-width: 500px;
            margin: 0 auto;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            backdrop-filter: blur(10px);
        }
        
        .form-group input::placeholder,
        .form-group textarea::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }
            
            .hero-content h1 {
                font-size: 2.5rem;
            }
            
            .btn {
                display: block;
                margin: 10px auto;
                width: fit-content;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="container">
            <div class="nav-content">
                <div class="logo">☁️ 云深处</div>
                <ul class="nav-links">
                    <li><a href="#home">首页</a></li>
                    <li><a href="#services">服务</a></li>
                    <li><a href="#contact">联系</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主页面 -->
    <section id="home" class="hero">
        <div class="clouds">
            <div class="cloud cloud1"></div>
            <div class="cloud cloud2"></div>
        </div>
        <div class="hero-content">
            <h1>云深处</h1>
            <p>探索云端科技的无限可能</p>
            <a href="#services" class="btn">开始探索</a>
            <a href="#contact" class="btn">联系我们</a>
        </div>
    </section>

    <!-- 服务区域 -->
    <section id="services" class="services">
        <div class="container">
            <h2>核心服务</h2>
            <div class="service-grid">
                <div class="service-card">
                    <div class="service-icon">☁️</div>
                    <h3>云存储</h3>
                    <p>安全可靠的云端存储解决方案</p>
                </div>
                <div class="service-card">
                    <div class="service-icon">🤖</div>
                    <h3>AI智能</h3>
                    <p>基于机器学习的智能分析平台</p>
                </div>
                <div class="service-card">
                    <div class="service-icon">🛡️</div>
                    <h3>安全防护</h3>
                    <p>全方位的网络安全防护体系</p>
                </div>
                <div class="service-card">
                    <div class="service-icon">📊</div>
                    <h3>数据分析</h3>
                    <p>强大的数据可视化和分析工具</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 联系区域 -->
    <section id="contact" class="contact">
        <div class="container">
            <h2>联系我们</h2>
            <form class="contact-form">
                <div class="form-group">
                    <input type="text" placeholder="您的姓名" required>
                </div>
                <div class="form-group">
                    <input type="email" placeholder="邮箱地址" required>
                </div>
                <div class="form-group">
                    <textarea placeholder="留言内容" rows="5" required></textarea>
                </div>
                <button type="submit" class="btn">发送消息</button>
            </form>
        </div>
    </section>

    <script>
        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // 表单提交
        document.querySelector('.contact-form').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('消息已发送！我们会尽快回复您。');
        });
    </script>
</body>
</html>
