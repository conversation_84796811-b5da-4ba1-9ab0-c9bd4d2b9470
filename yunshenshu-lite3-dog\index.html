<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>云深处 Lite3 小狗 App</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        .app-container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            position: relative;
        }
        
        /* 头部 */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }
        
        .header h1 {
            font-size: 1.5rem;
            margin-bottom: 5px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 0.9rem;
        }
        
        .cloud-icon {
            position: absolute;
            top: 15px;
            right: 20px;
            font-size: 1.5rem;
            animation: float 3s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        /* 小狗展示区 */
        .dog-display {
            padding: 30px 20px;
            text-align: center;
            background: #f8f9fa;
        }
        
        .dog-avatar {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 4rem;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .dog-avatar:hover {
            transform: scale(1.1);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .dog-avatar::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.3), transparent);
            transform: rotate(45deg);
            transition: all 0.5s;
            opacity: 0;
        }
        
        .dog-avatar:hover::before {
            animation: shine 0.5s ease-in-out;
        }
        
        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); opacity: 0; }
            50% { opacity: 1; }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); opacity: 0; }
        }
        
        .dog-name {
            font-size: 1.5rem;
            color: #2d3436;
            margin-bottom: 10px;
        }
        
        .dog-status {
            color: #636e72;
            margin-bottom: 20px;
        }
        
        /* 状态指示器 */
        .status-indicators {
            display: flex;
            justify-content: space-around;
            margin-bottom: 30px;
        }
        
        .status-item {
            text-align: center;
            flex: 1;
        }
        
        .status-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }
        
        .hunger { background: #e17055; }
        .happiness { background: #fdcb6e; }
        .energy { background: #00b894; }
        
        .status-bar {
            width: 100%;
            height: 8px;
            background: #ddd;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 5px;
        }
        
        .status-fill {
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        
        .hunger .status-fill { background: #e17055; }
        .happiness .status-fill { background: #fdcb6e; }
        .energy .status-fill { background: #00b894; }
        
        /* 操作按钮 */
        .actions {
            padding: 0 20px 30px;
        }
        
        .action-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .action-btn {
            background: white;
            border: 2px solid #ddd;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
            border-color: #667eea;
        }
        
        .action-btn:active {
            transform: translateY(0);
        }
        
        .action-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .action-text {
            color: #2d3436;
            font-weight: 500;
        }
        
        /* 消息提示 */
        .message {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            font-size: 1.1rem;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .message.show {
            opacity: 1;
        }
        
        /* 底部导航 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 400px;
            background: white;
            padding: 15px;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-around;
        }
        
        .nav-item {
            text-align: center;
            color: #636e72;
            cursor: pointer;
            transition: color 0.3s ease;
        }
        
        .nav-item:hover,
        .nav-item.active {
            color: #667eea;
        }
        
        .nav-item i {
            font-size: 1.5rem;
            margin-bottom: 5px;
            display: block;
        }
        
        /* 响应式 */
        @media (max-width: 450px) {
            .app-container,
            .bottom-nav {
                width: 100%;
            }
        }
    </style>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- 头部 -->
        <div class="header">
            <div class="cloud-icon">☁️</div>
            <h1>云深处 Lite3</h1>
            <p>你的专属小狗伙伴</p>
        </div>
        
        <!-- 小狗展示区 -->
        <div class="dog-display">
            <div class="dog-avatar" id="dogAvatar">
                🐕
            </div>
            <div class="dog-name" id="dogName">小云</div>
            <div class="dog-status" id="dogStatus">心情不错，想要玩耍！</div>
            
            <!-- 状态指示器 -->
            <div class="status-indicators">
                <div class="status-item">
                    <div class="status-icon hunger">
                        🍖
                    </div>
                    <div class="status-bar">
                        <div class="status-fill" id="hungerBar" style="width: 70%"></div>
                    </div>
                    <small>饥饿度</small>
                </div>
                <div class="status-item">
                    <div class="status-icon happiness">
                        😊
                    </div>
                    <div class="status-bar">
                        <div class="status-fill" id="happinessBar" style="width: 85%"></div>
                    </div>
                    <small>快乐度</small>
                </div>
                <div class="status-item">
                    <div class="status-icon energy">
                        ⚡
                    </div>
                    <div class="status-bar">
                        <div class="status-fill" id="energyBar" style="width: 60%"></div>
                    </div>
                    <small>精力值</small>
                </div>
            </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="actions">
            <div class="action-grid">
                <div class="action-btn" onclick="feedDog()">
                    <div class="action-icon">🍖</div>
                    <div class="action-text">喂食</div>
                </div>
                <div class="action-btn" onclick="playWithDog()">
                    <div class="action-icon">🎾</div>
                    <div class="action-text">玩耍</div>
                </div>
                <div class="action-btn" onclick="petDog()">
                    <div class="action-icon">❤️</div>
                    <div class="action-text">抚摸</div>
                </div>
                <div class="action-btn" onclick="restDog()">
                    <div class="action-icon">😴</div>
                    <div class="action-text">休息</div>
                </div>
            </div>
        </div>
        
        <!-- 底部导航 -->
        <div class="bottom-nav">
            <div class="nav-item active">
                <i class="fas fa-home"></i>
                <span>主页</span>
            </div>
            <div class="nav-item">
                <i class="fas fa-gamepad"></i>
                <span>游戏</span>
            </div>
            <div class="nav-item">
                <i class="fas fa-shopping-cart"></i>
                <span>商店</span>
            </div>
            <div class="nav-item">
                <i class="fas fa-cog"></i>
                <span>设置</span>
            </div>
        </div>
    </div>
    
    <!-- 消息提示 -->
    <div class="message" id="message"></div>
    
    <script>
        // 小狗状态
        let dogStats = {
            hunger: 70,
            happiness: 85,
            energy: 60
        };
        
        // 小狗表情和状态
        const dogEmojis = ['🐕', '🐶', '🦮', '🐕‍🦺'];
        const dogStatuses = [
            '心情不错，想要玩耍！',
            '有点饿了，需要食物',
            '精力充沛，准备冒险！',
            '感到疲惫，想要休息',
            '非常开心，摇着尾巴！',
            '正在思考人生...'
        ];
        
        let currentDogIndex = 0;
        
        // 显示消息
        function showMessage(text) {
            const message = document.getElementById('message');
            message.textContent = text;
            message.classList.add('show');
            setTimeout(() => {
                message.classList.remove('show');
            }, 2000);
        }
        
        // 更新状态条
        function updateStatusBars() {
            document.getElementById('hungerBar').style.width = dogStats.hunger + '%';
            document.getElementById('happinessBar').style.width = dogStats.happiness + '%';
            document.getElementById('energyBar').style.width = dogStats.energy + '%';
        }
        
        // 更新小狗状态
        function updateDogStatus() {
            const statusElement = document.getElementById('dogStatus');
            const randomStatus = dogStatuses[Math.floor(Math.random() * dogStatuses.length)];
            statusElement.textContent = randomStatus;
        }
        
        // 喂食
        function feedDog() {
            if (dogStats.hunger < 100) {
                dogStats.hunger = Math.min(100, dogStats.hunger + 20);
                dogStats.happiness = Math.min(100, dogStats.happiness + 10);
                showMessage('小云吃得很开心！🍖');
                updateStatusBars();
                updateDogStatus();
            } else {
                showMessage('小云已经吃饱了！');
            }
        }
        
        // 玩耍
        function playWithDog() {
            if (dogStats.energy > 20) {
                dogStats.energy = Math.max(0, dogStats.energy - 15);
                dogStats.happiness = Math.min(100, dogStats.happiness + 25);
                dogStats.hunger = Math.max(0, dogStats.hunger - 10);
                showMessage('和小云玩得很开心！🎾');
                updateStatusBars();
                updateDogStatus();
            } else {
                showMessage('小云太累了，需要休息！');
            }
        }
        
        // 抚摸
        function petDog() {
            dogStats.happiness = Math.min(100, dogStats.happiness + 15);
            showMessage('小云很享受你的抚摸！❤️');
            updateStatusBars();
            updateDogStatus();
            
            // 小狗头像动画
            const avatar = document.getElementById('dogAvatar');
            avatar.style.transform = 'scale(1.2)';
            setTimeout(() => {
                avatar.style.transform = 'scale(1)';
            }, 300);
        }
        
        // 休息
        function restDog() {
            dogStats.energy = Math.min(100, dogStats.energy + 30);
            dogStats.hunger = Math.max(0, dogStats.hunger - 5);
            showMessage('小云睡得很香！😴');
            updateStatusBars();
            updateDogStatus();
        }
        
        // 点击小狗头像
        document.getElementById('dogAvatar').addEventListener('click', function() {
            currentDogIndex = (currentDogIndex + 1) % dogEmojis.length;
            this.textContent = dogEmojis[currentDogIndex];
            showMessage('小云变了个样子！✨');
        });
        
        // 自动状态衰减
        setInterval(() => {
            dogStats.hunger = Math.max(0, dogStats.hunger - 1);
            dogStats.happiness = Math.max(0, dogStats.happiness - 0.5);
            dogStats.energy = Math.max(0, dogStats.energy - 0.5);
            updateStatusBars();
            
            // 随机更新状态
            if (Math.random() < 0.1) {
                updateDogStatus();
            }
        }, 5000);
        
        // 底部导航点击
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelector('.nav-item.active').classList.remove('active');
                this.classList.add('active');
                
                const text = this.querySelector('span').textContent;
                if (text !== '主页') {
                    showMessage(`${text}功能开发中...`);
                }
            });
        });
        
        // 初始化
        updateStatusBars();
        showMessage('欢迎来到云深处 Lite3！🐕');
    </script>
</body>
</html>
